import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { Asset } from '../modules/assets/entities/asset.entity';
import { Category } from '../modules/categories/entities/category.entity';
import { Tag } from '../modules/tags/entities/tag.entity';
import { AssetVersion } from '../modules/versions/entities/asset-version.entity';
import { User } from '../modules/auth/entities/user.entity';

@Injectable()
export class DatabaseConfig {
  constructor(private configService: ConfigService) {}

  createTypeOrmOptions(): TypeOrmModuleOptions {
    return {
      type: 'mysql',
      host: this.configService.get('DB_HOST', 'localhost'),
      port: parseInt(this.configService.get('DB_PORT', '3306')),
      username: this.configService.get('DB_USERNAME', 'root'),
      password: this.configService.get('DB_PASSWORD', 'password'),
      database: this.configService.get('DB_DATABASE_ASSET_LIBRARY', this.configService.get('DB_DATABASE', 'ir_engine_asset_library')),
      entities: [Asset, Category, Tag, AssetVersion, User],
      synchronize: this.configService.get('NODE_ENV') !== 'production',
      logging: this.configService.get('NODE_ENV') === 'development',
      charset: 'utf8mb4',
      timezone: '+08:00',
      migrations: ['dist/migrations/*.js'],
      migrationsRun: true,
      retryAttempts: 5,
      retryDelay: 3000,
      autoLoadEntities: true,
      keepConnectionAlive: true,
      extra: {
        authPlugin: 'mysql_native_password',
        ssl: false,
        max: parseInt(this.configService.get('DB_POOL_MAX', '20')),
        min: parseInt(this.configService.get('DB_POOL_MIN', '5')),
        acquire: parseInt(this.configService.get('DB_POOL_ACQUIRE_TIMEOUT', '60000')),
        idle: parseInt(this.configService.get('DB_POOL_IDLE_TIMEOUT', '30000')),
        timeout: 60000,
      },
    };
  }
}
