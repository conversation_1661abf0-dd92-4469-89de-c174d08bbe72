import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  DigitalHuman,
  KnowledgeBase,
  KnowledgeDocument,
  DocumentChunk,
  DigitalHumanKnowledgeBinding,
} from '../entities';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', 'password'),
        database: configService.get('DB_DATABASE_KNOWLEDGE', configService.get('DB_DATABASE', 'ir_engine_knowledge')),
        charset: 'utf8mb4',
        timezone: '+08:00',
        entities: [
          DigitalHuman,
          KnowledgeBase,
          KnowledgeDocument,
          DocumentChunk,
          DigitalHumanKnowledgeBinding,
        ],
        synchronize: process.env.NODE_ENV !== 'production',
        logging: process.env.NODE_ENV === 'development',
        retryAttempts: 5,
        retryDelay: 3000,
        autoLoadEntities: true,
        keepConnectionAlive: true,
        extra: {
          ssl: false,
          connectionLimit: parseInt(configService.get('DB_POOL_MAX', '20')),
          acquireTimeout: parseInt(configService.get('DB_POOL_ACQUIRE_TIMEOUT', '60000')),
          timeout: 60000,
          authPlugin: 'mysql_native_password',
        },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      DigitalHuman,
      KnowledgeBase,
      KnowledgeDocument,
      DocumentChunk,
      DigitalHumanKnowledgeBinding,
    ]),
  ],
  exports: [TypeOrmModule],
})
export class DatabaseModule {}
