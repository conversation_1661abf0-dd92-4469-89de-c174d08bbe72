#!/usr/bin/env pwsh
# 验证2024年12月19日修复的脚本
# 用于验证Docker容器配置修复是否成功

param(
    [switch]$Rebuild,     # 重新构建受影响的服务
    [switch]$Restart,     # 重启受影响的服务
    [switch]$CheckLogs,   # 检查服务日志
    [switch]$TestHealth,  # 测试健康检查
    [switch]$All          # 执行所有验证步骤
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔍 $message"
    Write-Host "=" * 60
}

# 受影响的服务列表
$affectedServices = @(
    @{
        Name = "knowledge-service"
        Container = "dl-engine-knowledge-service-win"
        Port = 8008
        HealthUrl = "http://localhost:8008/api/health"
    },
    @{
        Name = "ai-model-service"
        Container = "dl-engine-ai-model-service-win"
        Port = 3008
        HealthUrl = "http://localhost:3008/api/v1/health"
    },
    @{
        Name = "binding-service"
        Container = "dl-engine-binding-service-win"
        Port = 3011
        HealthUrl = "http://localhost:3011/health"
    },
    @{
        Name = "scene-generation-service"
        Container = "dl-engine-scene-generation-service-win"
        Port = 8005
        HealthUrl = "http://localhost:8005/health"
    },
    @{
        Name = "service-registry"
        Container = "dl-engine-service-registry-win"
        Port = 4010
        HealthUrl = "http://localhost:4010/api/health"
    }
)

# 检查Docker是否运行
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    } catch {
        return $false
    }
}

# 获取Docker Compose命令
function Get-DockerComposeCommand {
    try {
        docker-compose --version | Out-Null
        return "docker-compose"
    } catch {
        return "docker compose"
    }
}

# 重新构建服务
function Rebuild-Services {
    Write-Header "重新构建受影响的服务"
    
    $composeCmd = Get-DockerComposeCommand
    
    foreach ($service in $affectedServices) {
        Write-Info "重新构建服务: $($service.Name)"
        try {
            & $composeCmd -f docker-compose.windows.yml build --no-cache $service.Name
            Write-Success "$($service.Name) 构建成功"
        } catch {
            Write-Error "$($service.Name) 构建失败: $_"
        }
    }
}

# 重启服务
function Restart-Services {
    Write-Header "重启受影响的服务"
    
    $composeCmd = Get-DockerComposeCommand
    
    foreach ($service in $affectedServices) {
        Write-Info "重启服务: $($service.Name)"
        try {
            & $composeCmd -f docker-compose.windows.yml restart $service.Name
            Write-Success "$($service.Name) 重启成功"
            Start-Sleep -Seconds 10
        } catch {
            Write-Error "$($service.Name) 重启失败: $_"
        }
    }
}

# 检查服务日志
function Check-ServiceLogs {
    Write-Header "检查服务日志"
    
    $composeCmd = Get-DockerComposeCommand
    
    foreach ($service in $affectedServices) {
        Write-Info "检查 $($service.Name) 日志..."
        try {
            $logs = & $composeCmd -f docker-compose.windows.yml logs --tail=20 $service.Name 2>&1
            
            # 检查是否有错误信息
            $errorLines = $logs | Where-Object { $_ -match "ERROR|error|Error|FATAL|fatal|Fatal" }
            $warningLines = $logs | Where-Object { $_ -match "WARN|warn|Warn|WARNING|warning|Warning" }
            
            if ($errorLines) {
                Write-Warning "$($service.Name) 发现错误日志:"
                $errorLines | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
            } elseif ($warningLines) {
                Write-Warning "$($service.Name) 发现警告日志:"
                $warningLines | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
            } else {
                Write-Success "$($service.Name) 日志正常"
            }
        } catch {
            Write-Error "无法获取 $($service.Name) 日志: $_"
        }
        Write-Host ""
    }
}

# 测试健康检查
function Test-HealthChecks {
    Write-Header "测试服务健康检查"
    
    foreach ($service in $affectedServices) {
        Write-Info "测试 $($service.Name) 健康检查..."
        
        try {
            $response = Invoke-WebRequest -Uri $service.HealthUrl -Method GET -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Success "$($service.Name) 健康检查通过"
            } else {
                Write-Warning "$($service.Name) 健康检查异常: HTTP $($response.StatusCode)"
            }
        } catch {
            Write-Error "$($service.Name) 健康检查失败: $_"
        }
    }
}

# 检查端口连接
function Test-PortConnections {
    Write-Header "检查端口连接"
    
    foreach ($service in $affectedServices) {
        Write-Info "测试 $($service.Name) 端口 $($service.Port)..."
        
        try {
            $connection = Test-NetConnection -ComputerName localhost -Port $service.Port -WarningAction SilentlyContinue
            if ($connection.TcpTestSucceeded) {
                Write-Success "$($service.Name) 端口 $($service.Port) 连接正常"
            } else {
                Write-Error "$($service.Name) 端口 $($service.Port) 连接失败"
            }
        } catch {
            Write-Error "$($service.Name) 端口测试失败: $_"
        }
    }
}

# 检查容器状态
function Check-ContainerStatus {
    Write-Header "检查容器状态"
    
    foreach ($service in $affectedServices) {
        Write-Info "检查 $($service.Container) 状态..."
        
        try {
            $containerInfo = docker inspect $service.Container --format "{{.State.Status}}" 2>$null
            if ($containerInfo -eq "running") {
                Write-Success "$($service.Container) 运行正常"
            } else {
                Write-Error "$($service.Container) 状态异常: $containerInfo"
            }
        } catch {
            Write-Error "$($service.Container) 不存在或无法访问"
        }
    }
}

# 验证数据库连接
function Test-DatabaseConnections {
    Write-Header "验证数据库连接"
    
    Write-Info "检查MySQL数据库..."
    try {
        $databases = docker exec dl-engine-mysql-win mysql -u root -p${env:MYSQL_ROOT_PASSWORD} -e "SHOW DATABASES;" 2>$null | Select-String "ir_engine"
        if ($databases) {
            Write-Success "MySQL数据库连接正常，发现以下业务数据库:"
            $databases | ForEach-Object { Write-Host "  - $_" -ForegroundColor Green }
        } else {
            Write-Warning "MySQL连接正常，但未发现业务数据库"
        }
    } catch {
        Write-Error "MySQL数据库连接失败: $_"
    }
}

# 主函数
function Main {
    Write-Header "Docker容器配置修复验证 - 2024年12月19日"
    
    # 检查Docker状态
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker Desktop 未运行，请先启动 Docker Desktop"
        exit 1
    }
    Write-Success "Docker Desktop 运行正常"
    
    # 检查docker-compose.windows.yml文件
    if (-not (Test-Path "docker-compose.windows.yml")) {
        Write-Error "docker-compose.windows.yml文件不存在"
        exit 1
    }
    Write-Success "Docker Compose配置文件存在"
    
    # 执行验证步骤
    if ($All -or $Rebuild) {
        Rebuild-Services
    }
    
    if ($All -or $Restart) {
        Restart-Services
    }
    
    # 基本检查
    Check-ContainerStatus
    Test-PortConnections
    
    if ($All -or $TestHealth) {
        Test-HealthChecks
    }
    
    if ($All -or $CheckLogs) {
        Check-ServiceLogs
    }
    
    Test-DatabaseConnections
    
    Write-Header "验证完成"
    Write-Success "🎉 所有验证步骤已完成！"
    Write-Info "💡 如果发现问题，请查看上面的详细信息"
    Write-Info "💡 建议运行: .\verify-fixes-2024-12-19.ps1 -All 进行完整验证"
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "验证脚本执行失败: $($_.Exception.Message)"
    exit 1
}
