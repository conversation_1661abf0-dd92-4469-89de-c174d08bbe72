/**
 * 服务注册中心应用模块
 */
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { RegistryModule } from './registry/registry.module';
import { HealthModule } from './health/health.module';
import { MonitoringModule } from './monitoring/monitoring.module';
// import { EventBusModule } from '../../shared/event-bus';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 3306),
        username: configService.get<string>('DB_USERNAME', 'root'),
        password: configService.get<string>('DB_PASSWORD', 'password'),
        database: configService.get<string>('DB_DATABASE_REGISTRY', configService.get<string>('DB_DATABASE', 'ir_engine_registry')),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get<string>('NODE_ENV', 'development') !== 'production',
        logging: configService.get<string>('NODE_ENV', 'development') !== 'production',
        charset: 'utf8mb4',
        timezone: '+08:00',
        retryAttempts: 10,
        retryDelay: 3000,
        autoLoadEntities: true,
        keepConnectionAlive: true,
        extra: {
          authPlugin: 'mysql_native_password',
          ssl: false,
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
          reconnect: true,
          idleTimeout: 300000,
        },
      }),
    }),
    
    // 定时任务模块
    ScheduleModule.forRoot(),

    // 事件发射器模块
    EventEmitterModule.forRoot(),

    // 事件总线模块（暂时禁用以解决启动问题）
    // EventBusModule.register({
    //   serviceName: 'service-registry',
    //   redis: {
    //     host: process.env.REDIS_HOST || 'redis',
    //     port: parseInt(process.env.REDIS_PORT || '6379'),
    //     password: process.env.REDIS_PASSWORD || undefined,
    //     db: parseInt(process.env.REDIS_DB || '0'),
    //   },
    // }),

    // 功能模块
    AuthModule,
    RegistryModule,
    HealthModule,
    MonitoringModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
